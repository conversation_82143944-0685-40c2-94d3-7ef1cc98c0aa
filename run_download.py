#!/usr/bin/env python3
"""
简单的运行脚本，直接下载所有图片到同一目录
"""

import pandas as pd
from pathlib import Path
from afts import Afts
import time

# AFTS配置
endpoint_config = {
    "upload_endpoint_source": "mass.alipay.com",
    "download_endpoint_source": "mdn.alipayobjects.com",
    "authority_endpoint": "mmtcapi.alipay.com"
}
biz_key = "content_liveface"
biz_secret = "31cedb0f8bc24b778af865d8c5704705"

_afts = Afts(
    biz_key=biz_key,
    biz_secret=biz_secret,
    endpoint_config=endpoint_config
)

def download_image_from_afts(afts_id, save_path):
    """从AFTS下载图片并保存"""
    try:
        print(f"正在下载 {afts_id}...")
        content = _afts.download_file(afts_id)
        
        with open(save_path, 'wb') as f:
            f.write(content)
        
        print(f"成功保存到 {save_path}")
        return True
    except Exception as e:
        print(f"下载失败 {afts_id}: {e}")
        return False

def main():
    # 配置
    csv_file = "csv/test-0819.csv"
    output_dir = "downloaded_images"
    max_downloads = 10  # 设置为None下载全部，或者设置数字限制下载数量
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    print(f"开始从 {csv_file} 下载图片...")
    print(f"图片保存目录: {output_path}")
    
    # 读取CSV文件
    df = pd.read_csv(csv_file)
    
    if max_downloads:
        df = df.head(max_downloads)
        print(f"限制下载数量: {max_downloads}")
    
    total_rows = len(df)
    successful_covers = 0
    successful_smart_covers = 0
    failed_downloads = []
    
    print(f"总共需要处理 {total_rows} 行数据")
    
    for i, row in df.iterrows():
        ctt_id = row['ctt_id']
        cover_id = row['cover_id']
        smart_cover_id = row['smart_cover_id']
        
        print(f"\n处理第 {i+1}/{total_rows} 行: {ctt_id}")
        
        # 下载封面
        if pd.notna(cover_id) and cover_id:
            cover_filename = f"{ctt_id}_cover.jpg"
            cover_path = output_path / cover_filename
            
            if download_image_from_afts(cover_id, cover_path):
                successful_covers += 1
            else:
                failed_downloads.append(f"封面: {ctt_id} - {cover_id}")
        
        # 下载智能封面
        if pd.notna(smart_cover_id) and smart_cover_id:
            smart_cover_filename = f"{ctt_id}_smart_cover.jpg"
            smart_cover_path = output_path / smart_cover_filename
            
            if download_image_from_afts(smart_cover_id, smart_cover_path):
                successful_smart_covers += 1
            else:
                failed_downloads.append(f"智能封面: {ctt_id} - {smart_cover_id}")
        
        # 添加延迟避免请求过于频繁
        time.sleep(0.2)
    
    # 输出统计信息
    print(f"\n=== 下载完成 ===")
    print(f"总处理行数: {total_rows}")
    print(f"成功下载封面: {successful_covers}")
    print(f"成功下载智能封面: {successful_smart_covers}")
    print(f"失败下载数: {len(failed_downloads)}")
    
    if failed_downloads:
        print(f"\n失败的下载:")
        for failed in failed_downloads:
            print(f"  - {failed}")

if __name__ == "__main__":
    main()
