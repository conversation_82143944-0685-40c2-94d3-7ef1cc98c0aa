#!/usr/bin/env python3
"""
简化版本的下载脚本，用于测试
"""

import os
from pathlib import Path
import pandas as pd
from afts import Afts
from PIL import Image
from io import BytesIO
import time

# 使用test_afts.py中的配置
endpoint_config = {
    "upload_endpoint_source": "mass.alipay.com",
    "download_endpoint_source": "mdn.alipayobjects.com",
    "authority_endpoint": "mmtcapi.alipay.com"
}
biz_key = "content_liveface"
biz_secret = "31cedb0f8bc24b778af865d8c5704705"

_afts = Afts(
    biz_key=biz_key,
    biz_secret=biz_secret,
    endpoint_config=endpoint_config
)

def download_image_from_afts(afts_id, save_path):
    """从AFTS下载图片并保存"""
    try:
        print(f"正在下载 {afts_id}...")
        content = _afts.download_file(afts_id)
        
        with open(save_path, 'wb') as f:
            f.write(content)
        
        print(f"成功保存到 {save_path}")
        return True
    except Exception as e:
        print(f"下载失败 {afts_id}: {e}")
        return False

def main():
    """测试下载前几行数据"""
    csv_file = "csv/test-0819.csv"
    output_dir = "test_downloads"

    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)

    print(f"测试下载前3行数据...")
    print(f"图片保存目录: {output_path}")

    # 使用pandas读取CSV文件
    df = pd.read_csv(csv_file)

    # 只处理前3行
    df = df.head(3)

    for i, row in df.iterrows():
        ctt_id = row['ctt_id']
        cover_id = row['cover_id']
        smart_cover_id = row['smart_cover_id']

        print(f"\n处理第 {i+1} 行: {ctt_id}")

        # 下载封面
        if pd.notna(cover_id) and cover_id:
            cover_path = output_path / f"{ctt_id}_cover.jpg"
            download_image_from_afts(cover_id, cover_path)

        # 下载智能封面
        if pd.notna(smart_cover_id) and smart_cover_id:
            smart_cover_path = output_path / f"{ctt_id}_smart_cover.jpg"
            download_image_from_afts(smart_cover_id, smart_cover_path)

        time.sleep(0.5)  # 延迟避免请求过频

if __name__ == "__main__":
    main()
