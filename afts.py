# -*- coding: utf-8 -*-

"""
Copyright (C) 2020 antfin.com. All rights reserved.

@file: afts.py
@author: 轩勇
@date: 20200603
"""

import time
from enum import Enum
import requests
import base64
import hashlib
from datetime import datetime
from typing import Optional, Dict, Union, Any
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Constants
DEFAULT_EXPIRE_TIME = 86400  # 1 day in seconds
MAX_EXPIRE_TIME = 7 * 24 * 3600  # 7 days in seconds
DEFAULT_ENDPOINTS = {
    "upload_endpoint_source": "mass.alipay.com",
    "download_endpoint_source": "mdn.alipayobjects.com",
    "authority_endpoint": "mmtcapi.alipay.com"
}


class FileSecLevel(Enum):
    """File security level enumeration."""
    PRIVATE = 0  # 私密文件
    PUBLIC = 1   # 公开文件
    AUTH = 3     # 鉴权文件


class AftsError(Exception):
    """Custom exception for Afts-related errors."""
    pass


class Afts:
    """
    This class is used to upload and/or download file data to/from afts.
    The public api is "download_file" and "upload_file", these two function will not throw exception.
    """

    def __init__(self, biz_key: str, biz_secret: str, endpoint_config: Dict[str, str] = None):
        """
        Initialize Afts client.
        
        Args:
            biz_key: Business key
            biz_secret: Business secret
            endpoint_config: Optional endpoint configuration
        """
        self.__biz_key = biz_key
        self.__biz_secret = biz_secret
        self.__appid = "apwallet"
        self.__err_msg = ""
        
        # Merge default endpoints with provided config
        self.__endpoints = DEFAULT_ENDPOINTS.copy()
        if endpoint_config:
            self.__endpoints.update(endpoint_config)
            
        self.__upload_endpoint_source = self.__endpoints["upload_endpoint_source"]
        self.__download_endpoint_source = self.__endpoints["download_endpoint_source"]
        self.__authority_endpoint = self.__endpoints["authority_endpoint"]

    def err_msg(self) -> str:
        """Return error message."""
        return self.__err_msg

    def _make_request(self, method: str, url: str, **kwargs) -> requests.Response:
        """
        Make HTTP request with error handling and logging.
        
        Args:
            method: HTTP method
            url: Request URL
            **kwargs: Additional arguments for requests
            
        Returns:
            Response object
            
        Raises:
            AftsError: If request fails
        """
        start_time = time.time()
        query_str = ""
        try:
            # Format query params if present
            if "params" in kwargs:
                query_str = "?" + "&".join([f"{k}={v}" for k, v in kwargs["params"].items()])

            response = requests.request(method, url, **kwargs)
            end_time = time.time()
            duration = (end_time - start_time) * 1000
            
            if response.status_code != 200:
                logger.error(
                    f"Request failed: {method} {url} - Status: {response.status_code} - Duration: {duration}ms"
                )
                raise AftsError(f"HTTP request failed with status {response.status_code}: {response.text}")
            
            request_id = response.headers.get('request-id') or response.headers.get('trace-id')
            logger.info(
                f"Request successful: {method} {url}{query_str} - Duration: {duration}ms - Request-ID: {request_id}"
            )
            return response
            
        except requests.RequestException as e:
            logger.error(f"Request error: {method} {url}{query_str} - {str(e)}")
            raise AftsError(f"Request failed: {str(e)}")

    def get_op_token(self) -> Optional[str]:
        """Get operation token."""
        time_stamp = str(int(time.time() * 1000))
        authority_url = f"https://{self.__authority_endpoint}/token/1.0/op"
        
        url_params = {
            "timestamp": time_stamp,
            "bizKey": self.__biz_key,
            "appId": self.__appid
        }
        
        sign_str = f"{self.__appid}{self.__biz_key}{time_stamp}{self.__biz_secret}"
        url_params["sign"] = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
        
        try:
            response = self._make_request("GET", authority_url, params=url_params)
            res_json = response.json()
            
            if res_json["code"] != 0:
                self.__err_msg = f"Error: get_op_token: server response code != 0, code: {res_json['code']}"
                return None
                
            return res_json["data"]["token"]
            
        except (AftsError, KeyError, ValueError) as e:
            self.__err_msg = f"Error: get_op_token: {str(e)}"
            return None

    def get_acl_token(self, file_id: str, expire_time: int = DEFAULT_EXPIRE_TIME) -> Optional[str]:
        """
        Get ACL token.
        
        Args:
            file_id: File ID
            expire_time: Expiration time in seconds (default: 1 day, max: 7 days)
            
        Returns:
            ACL token data or None if failed
        """
        if expire_time > MAX_EXPIRE_TIME:
            self.__err_msg = f"Error: expire_time cannot exceed {MAX_EXPIRE_TIME} seconds"
            return None
            
        time_stamp = str(int(time.time() * 1000))
        authority_url = f"https://{self.__authority_endpoint}/token/1.0/acl"
        
        url_params = {
            "timestamp": time_stamp,
            "bizKey": self.__biz_key,
            "fileId": file_id,
            "appId": self.__appid,
            "survivalTime": expire_time
        }
        
        sign_str = f"{self.__appid}{self.__biz_key}{time_stamp}{file_id}{self.__biz_secret}{expire_time}"
        logger.debug(f"sign_str={sign_str}")
        url_params["sign"] = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
        
        try:
            response = self._make_request("GET", authority_url, params=url_params)
            res_json = response.json()
            
            if res_json["code"] != 0:
                self.__err_msg = f"Error: get_acl_token: server response code != 0, code: {res_json['code']}"
                return None
                
            return res_json["data"]
            
        except (AftsError, KeyError, ValueError) as e:
            self.__err_msg = f"Error: get_acl_token: {str(e)}"
            return None

    def get_mass_token(self) -> Optional[str]:
        """Get mass token."""
        time_stamp = str(int(time.time() * 1000))
        authority_url = f"https://{self.__authority_endpoint}/token/1.0/mass"
        
        op_token = self.get_op_token()
        if not op_token:
            return None
            
        url_params = {
            "appId": self.__appid,
            "bizKey": self.__biz_key,
            "opToken": op_token,
            "massType": '1',
            "timestamp": time_stamp,
            "value": self.__biz_key
        }
        
        sign_str = f"{self.__appid}{self.__biz_key}{url_params['value']}{time_stamp}"
        url_params["sign"] = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
        
        try:
            response = self._make_request("GET", authority_url, params=url_params)
            res_json = response.json()
            
            if res_json["code"] != 0:
                self.__err_msg = f"Error: get_mass_token: server response code != 0, code: {res_json['code']}"
                return None
                
            return res_json["data"]
            
        except (AftsError, KeyError, ValueError) as e:
            self.__err_msg = f"Error: get_mass_token: {str(e)}"
            return None

    def __parse_file_id(self, file_id: str) -> Optional[FileSecLevel]:
        """
        Parse file ID to determine security level.
        
        Args:
            file_id: File ID to parse
            
        Returns:
            FileSecLevel enum value or None if parsing fails
        """
        try:
            decoded_data = ""
            suffix_label = file_id[-1]
            
            if suffix_label in ["A", "Q", "w"]:
                decoded_data = base64.urlsafe_b64decode(file_id[2:] + '===')
            elif suffix_label == 'r':
                decoded_data = base64.urlsafe_b64decode(file_id)
            else:
                self.__err_msg = f"Error: Invalid file ID suffix: {suffix_label}"
                return None

            file_sec_level = decoded_data[21] & 0x0F
            logger.debug(f"file_id={file_id} file_sec_level={file_sec_level}")
            return FileSecLevel(file_sec_level)
            
        except Exception as e:
            logger.error(f"Error parsing file ID: {str(e)}")
            self.__err_msg = f"Error parsing file ID: {str(e)}"
            return None

    def __download_file(self, file_id: str) -> Optional[bytes]:
        """
        Download file content.
        
        Args:
            file_id: File ID to download
            
        Returns:
            File content as bytes or None if download fails
        """
        download_url = f"https://{self.__download_endpoint_source}/{self.__biz_key}/afts/file/{file_id}"
        url_params = {}
        
        file_sec_level = self.__parse_file_id(file_id)
        if file_sec_level in (FileSecLevel.PRIVATE, FileSecLevel.AUTH):
            acl_token = self.get_acl_token(file_id)
            if not acl_token:
                return None
            url_params["token"] = acl_token

        try:
            response = self._make_request("GET", download_url, params=url_params, allow_redirects=False)
            return response.content
            
        except AftsError as e:
            self.__err_msg = str(e)
            return None

    def __upload_file(
        self, 
        file_data: Union[bytes, str], 
        file_name: str, 
        setpublic: bool = False,
        alias: Optional[str] = None, 
        update_alias: bool = True
    ) -> Optional[str]:
        """
        Upload file with metadata.
        
        Args:
            file_data: File content
            file_name: Name of the file
            setpublic: Whether to make file public
            alias: Optional file alias
            update_alias: Whether to update existing alias
            
        Returns:
            File ID if successful, None otherwise
        """
        mass_token = self.get_mass_token()
        if not mass_token:
            return None
            
        upload_url = f"https://{self.__upload_endpoint_source}/file/auth/upload"
        
        url_params = {
            "bz": self.__biz_key,
            "public": str(setpublic).lower(),
            "mt": mass_token
        }
        
        if alias:
            url_params["alias"] = alias
            if update_alias:
                url_params["updateAlias"] = "true"
                
        form_file = {"file": (file_name, file_data, "application/octet-stream")}
        
        try:
            response = self._make_request("POST", upload_url, params=url_params, files=form_file)
            res_json = response.json()
            
            if res_json["code"] != 0:
                self.__err_msg = f"Error: __upload_file: server response code != 0, code: {res_json['code']}"
                return None
                
            return res_json["data"]["id"]
            
        except (AftsError, KeyError, ValueError) as e:
            self.__err_msg = f"Error: __upload_file: {str(e)}"
            return None

    def download_file(self, file_id: str) -> Optional[bytes]:
        """
        Download file by ID.
        
        Args:
            file_id: File ID
            
        Returns:
            File content if successful, None otherwise
        """
        try:
            return self.__download_file(file_id)
        except Exception as e:
            self.__err_msg = repr(e)
            return None

    def upload_file(
        self, 
        file_data: Union[bytes, str], 
        file_name: str, 
        setpublic: bool = False,
        alias: Optional[str] = None, 
        update_alias: bool = True
    ) -> Optional[str]:
        """
        Upload file with metadata.
        
        Args:
            file_data: File content
            file_name: Name of the file
            setpublic: Whether to make file public
            alias: Optional file alias
            update_alias: Whether to update existing alias
            
        Returns:
            File ID if successful, None otherwise
        """
        try:
            return self.__upload_file(file_data, file_name, setpublic, alias, update_alias)
        except Exception as e:
            self.__err_msg = repr(e)
            return None

    def get_url(self, file_id: str, expire_time: int = DEFAULT_EXPIRE_TIME) -> Optional[str]:
        """
        Get file URL.
        
        Args:
            file_id: File ID
            expire_time: URL expiration time in seconds
            
        Returns:
            File URL if successful, None otherwise
        """
        if expire_time > MAX_EXPIRE_TIME:
            self.__err_msg = f"Error: expire_time cannot exceed {MAX_EXPIRE_TIME} seconds"
            return None
            
        download_url = f"https://{self.__download_endpoint_source}/{self.__biz_key}/afts/file/{file_id}"
        
        file_sec_level = self.__parse_file_id(file_id)
        if file_sec_level in (FileSecLevel.PRIVATE, FileSecLevel.AUTH):
            acl_token = self.get_acl_token(file_id, expire_time)
            if not acl_token:
                return None
            download_url = f"{download_url}?token={acl_token}"
            
        return download_url

    def get_url_by_alias(self, alias: str) -> str:
        """
        Get file URL by alias.
        
        Args:
            alias: File alias
            
        Returns:
            File URL
        """
        return f"https://{self.__download_endpoint_source}/{self.__biz_key}/uri/file/as/{alias}"
