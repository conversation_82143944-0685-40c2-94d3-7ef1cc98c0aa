#!/usr/bin/env python3
"""
脚本用于从CSV文件中下载封面和智能封面配对图片数据
"""

import csv
import os
import sys
from pathlib import Path
from afts import Afts
from PIL import Image
from io import BytesIO
import time

# AFTS配置
endpoint_config = {
    "upload_endpoint_source": "mass.alipay.com",
    "download_endpoint_source": "mdn.alipayobjects.com",
    "authority_endpoint": "mmtcapi.alipay.com"
}
biz_key = "content_liveface"
biz_secret = "31cedb0f8bc24b778af865d8c5704705"

# 初始化AFTS客户端
_afts = Afts(
    biz_key=biz_key,
    biz_secret=biz_secret,
    endpoint_config=endpoint_config
)

def download_image_from_afts(afts_id, save_path):
    """
    从AFTS下载图片并保存到指定路径
    
    Args:
        afts_id (str): AFTS文件ID
        save_path (str): 保存路径
    
    Returns:
        bool: 下载是否成功
    """
    try:
        print(f"正在下载 {afts_id}...")
        content = _afts.download_file(afts_id)
        
        # 保存图片
        with open(save_path, 'wb') as f:
            f.write(content)
        
        print(f"成功保存到 {save_path}")
        return True
    except Exception as e:
        print(f"下载失败 {afts_id}: {e}")
        return False

def create_output_dirs(base_dir):
    """创建输出目录"""
    cover_dir = Path(base_dir) / "covers"
    smart_cover_dir = Path(base_dir) / "smart_covers"
    
    cover_dir.mkdir(parents=True, exist_ok=True)
    smart_cover_dir.mkdir(parents=True, exist_ok=True)
    
    return cover_dir, smart_cover_dir

def download_cover_pairs(csv_file, output_dir="downloaded_images", max_downloads=None):
    """
    从CSV文件下载封面和智能封面配对图片
    
    Args:
        csv_file (str): CSV文件路径
        output_dir (str): 输出目录
        max_downloads (int): 最大下载数量，None表示下载全部
    """
    # 创建输出目录
    cover_dir, smart_cover_dir = create_output_dirs(output_dir)
    
    # 统计信息
    total_rows = 0
    successful_covers = 0
    successful_smart_covers = 0
    failed_downloads = []
    
    print(f"开始从 {csv_file} 下载图片...")
    print(f"封面保存目录: {cover_dir}")
    print(f"智能封面保存目录: {smart_cover_dir}")
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for i, row in enumerate(reader):
                if max_downloads and i >= max_downloads:
                    print(f"已达到最大下载数量限制: {max_downloads}")
                    break
                
                total_rows += 1
                ctt_id = row['ctt_id']
                cover_id = row['cover_id']
                smart_cover_id = row['smart_cover_id']
                
                print(f"\n处理第 {i+1} 行: {ctt_id}")
                
                # 下载封面
                if cover_id:
                    cover_filename = f"{ctt_id}_cover.jpg"
                    cover_path = cover_dir / cover_filename
                    
                    if download_image_from_afts(cover_id, cover_path):
                        successful_covers += 1
                    else:
                        failed_downloads.append(f"封面: {ctt_id} - {cover_id}")
                
                # 下载智能封面
                if smart_cover_id:
                    smart_cover_filename = f"{ctt_id}_smart_cover.jpg"
                    smart_cover_path = smart_cover_dir / smart_cover_filename
                    
                    if download_image_from_afts(smart_cover_id, smart_cover_path):
                        successful_smart_covers += 1
                    else:
                        failed_downloads.append(f"智能封面: {ctt_id} - {smart_cover_id}")
                
                # 添加延迟避免请求过于频繁
                time.sleep(0.1)
    
    except FileNotFoundError:
        print(f"错误: 找不到CSV文件 {csv_file}")
        return
    except Exception as e:
        print(f"处理CSV文件时出错: {e}")
        return
    
    # 输出统计信息
    print(f"\n=== 下载完成 ===")
    print(f"总处理行数: {total_rows}")
    print(f"成功下载封面: {successful_covers}")
    print(f"成功下载智能封面: {successful_smart_covers}")
    print(f"失败下载数: {len(failed_downloads)}")
    
    if failed_downloads:
        print(f"\n失败的下载:")
        for failed in failed_downloads:
            print(f"  - {failed}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='下载封面和智能封面配对图片')
    parser.add_argument('csv_file', help='CSV文件路径')
    parser.add_argument('-o', '--output', default='downloaded_images', 
                       help='输出目录 (默认: downloaded_images)')
    parser.add_argument('-n', '--max-downloads', type=int, 
                       help='最大下载数量限制')
    
    args = parser.parse_args()
    
    # 检查CSV文件是否存在
    if not os.path.exists(args.csv_file):
        print(f"错误: CSV文件不存在: {args.csv_file}")
        sys.exit(1)
    
    # 开始下载
    download_cover_pairs(args.csv_file, args.output, args.max_downloads)

if __name__ == "__main__":
    main()
